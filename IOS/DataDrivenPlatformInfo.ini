[DataDrivenPlatformInfo]
bIsConfidential=false
TargetSettingsIniSectionName=/Script/IOSRuntimeSettings.IOSRuntimeSettings
bHasDedicatedGamepad=false
bInputSupportConfigurable=false
DefaultInputType=Touch
bSupportsMouseAndKeyboard=false
bSupportsGamepad=true
bCanChangeGamepadType=true
bSupportsTouch=true
GlobalIdentifier=988EBA73F971495BAFB09639F8C796BD

NormalIconPath=Launcher/IOS/Platform_IOS_24x
LargeIconPath=Launcher/IOS/Platform_IOS_128x
XLargeIconPath=
AutoSDKPath=
Windows:TutorialPath=SharingAndReleasing/Mobile/iOS
Mac:TutorialPath=SharingAndReleasing/Mobile/iOS
Linux:TutorialPath=SharingAndReleasing/Mobile/iOS
Windows:bIsEnabled=true
Mac:bIsEnabled=true
Linux:bIsEnabled=false
bUsesHostCompiler=false
bUATClosesAfterLaunch=true
PlatformGroupName=Mobile

[PreviewPlatform IOSMetal]
PlatformName=IOS
ShaderFormat=SF_METAL_ES3_1_IOS
ShaderPlatform=METAL_ES3_1_IOS
ActiveIconName=LevelEditor.PreviewMode.iOS.Enabled
InactiveIconName=LevelEditor.PreviewMode.iOS.Disabled
MenuTooltip=NSLOCTEXT("PreviewPlatform", "PreviewMenuTooltip_iOS", "Mobile preview using iOS material quality settings.")
IconText=NSLOCTEXT("PreviewPlatform", "PreviewIconText_iOS", "iOS")
DeviceProfileName=IOS:iPhone6S:iPadPro6_129
FriendlyName=NSLOCTEXT("PreviewPlatform", "PreviewFriendlyName_GenericIOS", "Generic IOS"):NSLOCTEXT("PreviewPlatform", "PreviewFriendlyName_iPhone6S", "iPhone 6S"):NSLOCTEXT("PreviewPlatform", "PreviewFriendlyName_iPadPro612.9in", "iPad Pro 6 12.9in")

[PreviewPlatform IOSMetalSM5]
PlatformName=IOS
ShaderFormat=SF_METAL_SM5_IOS
ShaderPlatform=METAL_SM5_IOS
EnabledCVar=ini:Engine:IOS:/Script/IOSRuntimeSettings.IOSRuntimeSettings:bSupportsMetalMRT
ActiveIconName=LevelEditor.PreviewMode.iOSSM5.Enabled
InactiveIconName=LevelEditor.PreviewMode.iOSSM5.Disabled
MenuTooltip=NSLOCTEXT("PreviewPlatform", "PreviewMenuTooltip_iOSSM5", "Mobile preview using iOS SM5 material quality settings.")
IconText=NSLOCTEXT("PreviewPlatform", "PreviewIconText_iOSSM5", "iOS SM5")
DeviceProfileName=IOS

[ShaderPlatform METAL_ES3_1_IOS]
Language=Metal
MaxFeatureLevel=ES3_1
ShaderFormat=SF_METAL_ES3_1_IOS
bIsMobile=true
bTargetsTiledGPU=true
bSupports4ComponentUAVReadWrite=true
bSupportsManualVertexFetch=false
bSupportsVolumeTextureAtomics=false
bSupportsShaderPipelines = false
bSupportsIndependentSamplers=true
bSupportsComputeFramework = true
FriendlyName=LOCTEXT("FriendlyShaderPlatformName_IOSMobile", "IOS Metal Mobile")

[ShaderPlatform METAL_SM5_IOS]
Language=Metal
MaxFeatureLevel=SM5
ShaderFormat=SF_METAL_SM5_IOS
bIsMetalMRT=true
bTargetsTiledGPU=true
bSupportsIndexBufferUAVs=true
bSupports4ComponentUAVReadWrite=true
bSupportsManualVertexFetch=false
bSupportsVolumeTextureAtomics=false
bSupportsDistanceFields=true
bSupportsIndependentSamplers=true
bSupportsComputeFramework = true
FriendlyName=LOCTEXT("FriendlyShaderPlatformName_IOSSM5", "IOS Metal SM5")

[ShaderPlatform METAL_SIM]
Language=Metal
MaxFeatureLevel=ES3_1
ShaderFormat=SF_METAL_SIM
bIsMobile=true
bTargetsTiledGPU=true
bSupports4ComponentUAVReadWrite=true
bSupportsManualVertexFetch=false
bSupportsVolumeTextureAtomics=false
bSupportsShaderPipelines = false
bSupportsIndependentSamplers=true
bSupportsComputeFramework = true
FriendlyName=LOCTEXT("FriendlyShaderPlatformName_IOSMobileSIM", "IOS Metal Simulator Mobile")
